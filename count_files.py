#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统计各个文件夹中的文件数量
"""

import os

def count_files_in_directory(directory):
    """统计目录中的文件数量"""
    if not os.path.exists(directory):
        return 0
    
    count = 0
    for filename in os.listdir(directory):
        filepath = os.path.join(directory, filename)
        if os.path.isfile(filepath):
            count += 1
    return count

def list_files_in_directory(directory, limit=10):
    """列出目录中的文件（限制数量）"""
    if not os.path.exists(directory):
        return []
    
    files = []
    for filename in os.listdir(directory):
        filepath = os.path.join(directory, filename)
        if os.path.isfile(filepath):
            files.append(filename)
    
    return files[:limit]

def main():
    directories = ['images', 'mobile', 'question', 'scanned', 'answers']
    
    print("各文件夹文件统计：")
    print("=" * 50)
    
    total_files = 0
    
    for directory in directories:
        count = count_files_in_directory(directory)
        total_files += count
        print(f"{directory:10}: {count:3} 个文件")
        
        # 显示前几个文件作为示例
        if count > 0:
            sample_files = list_files_in_directory(directory, 5)
            print(f"           示例: {', '.join(sample_files[:3])}")
            if count > 3:
                print(f"           ...还有 {count-3} 个文件")
        print()
    
    print("=" * 50)
    print(f"总计: {total_files} 个文件")
    
    # 统计图片文件
    image_extensions = ['.png', '.jpg', '.jpeg']
    image_count = 0
    
    for directory in ['images', 'mobile', 'question', 'scanned']:
        if os.path.exists(directory):
            for filename in os.listdir(directory):
                if any(filename.lower().endswith(ext) for ext in image_extensions):
                    image_count += 1
    
    print(f"图片文件总数: {image_count} 个")

if __name__ == "__main__":
    main()
