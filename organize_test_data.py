#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据整理脚本
将scanned、question、mobile三个文件夹中的图片与answers文件夹中的标注答案进行配对整理
"""

import os
import shutil
import re
from pathlib import Path
from collections import defaultdict

def extract_id_from_filename(filename):
    """从文件名中提取ID"""
    # 匹配格式如: 2025050011-q.png, 2025050011-s-1.png 等
    match = re.match(r'(\d{10})', filename)
    if match:
        return match.group(1)
    return None

def get_file_type_from_filename(filename):
    """从文件名中提取文件类型 (q, s, ta等)"""
    # 匹配 -q, -s-1, -ta 等模式
    match = re.search(r'-([qsta]+)(?:-(\d+))?', filename)
    if match:
        file_type = match.group(1)
        number = match.group(2) if match.group(2) else ""
        return f"{file_type}{'-' + number if number else ''}"
    return "unknown"

def scan_directories():
    """扫描目录，只收集scanned、mobile、question中的图片及其对应答案"""
    data_map = defaultdict(lambda: defaultdict(list))
    target_ids = set()  # 存储在三个目标文件夹中找到的ID

    # 第一步：扫描scanned, mobile, question三个文件夹，收集所有ID
    image_dirs = ['scanned', 'mobile', 'question']
    for dir_name in image_dirs:
        if os.path.exists(dir_name):
            print(f"扫描 {dir_name} 文件夹...")
            for filename in os.listdir(dir_name):
                if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                    file_id = extract_id_from_filename(filename)
                    if file_id:
                        target_ids.add(file_id)
                        file_type = get_file_type_from_filename(filename)
                        data_map[file_id][f'{dir_name}_images'].append({
                            'filename': filename,
                            'path': os.path.join(dir_name, filename),
                            'type': file_type
                        })
                        print(f"  找到图片: {filename} (ID: {file_id})")

    print(f"总共找到 {len(target_ids)} 个不同的ID: {sorted(target_ids)}")

    # 第二步：只为这些ID在answers文件夹中查找对应的答案
    if os.path.exists('answers'):
        print("在answers文件夹中查找对应答案...")
        for filename in os.listdir('answers'):
            if filename.lower().endswith(('.txt', '.docx')):
                file_id = extract_id_from_filename(filename)
                if file_id and file_id in target_ids:  # 只处理目标ID的答案
                    file_type = get_file_type_from_filename(filename)
                    data_map[file_id]['answers'].append({
                        'filename': filename,
                        'path': os.path.join('answers', filename),
                        'type': file_type
                    })
                    print(f"  找到答案: {filename} (ID: {file_id})")

    # 只返回有图片的ID数据
    filtered_data_map = {file_id: data for file_id, data in data_map.items() if file_id in target_ids}
    return filtered_data_map

def create_organized_structure(data_map, output_dir='organized_test_data'):
    """创建整理后的目录结构"""
    if os.path.exists(output_dir):
        print(f"目录 {output_dir} 已存在，将清空后重新创建...")
        shutil.rmtree(output_dir)
    
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建统计信息
    stats = {
        'total_ids': len(data_map),
        'copied_files': 0,
        'missing_answers': [],
        'missing_images': []
    }
    
    for file_id, file_data in sorted(data_map.items()):
        # 为每个ID创建子目录
        id_dir = os.path.join(output_dir, file_id)
        os.makedirs(id_dir, exist_ok=True)

        # 创建子目录
        scanned_dir = os.path.join(id_dir, 'scanned')
        mobile_dir = os.path.join(id_dir, 'mobile')
        question_dir = os.path.join(id_dir, 'question')
        answers_dir = os.path.join(id_dir, 'answers')
        os.makedirs(scanned_dir, exist_ok=True)
        os.makedirs(mobile_dir, exist_ok=True)
        os.makedirs(question_dir, exist_ok=True)
        os.makedirs(answers_dir, exist_ok=True)
        
        # 复制图片文件到对应的子文件夹
        has_images = False

        # 复制scanned图片
        if 'scanned_images' in file_data:
            for file_info in file_data['scanned_images']:
                src_path = file_info['path']
                dst_path = os.path.join(scanned_dir, file_info['filename'])
                try:
                    shutil.copy2(src_path, dst_path)
                    stats['copied_files'] += 1
                    has_images = True
                    print(f"复制scanned图片: {src_path} -> {dst_path}")
                except Exception as e:
                    print(f"复制scanned图片失败: {src_path} - {e}")

        # 复制mobile图片
        if 'mobile_images' in file_data:
            for file_info in file_data['mobile_images']:
                src_path = file_info['path']
                dst_path = os.path.join(mobile_dir, file_info['filename'])
                try:
                    shutil.copy2(src_path, dst_path)
                    stats['copied_files'] += 1
                    has_images = True
                    print(f"复制mobile图片: {src_path} -> {dst_path}")
                except Exception as e:
                    print(f"复制mobile图片失败: {src_path} - {e}")

        # 复制question图片
        if 'question_images' in file_data:
            for file_info in file_data['question_images']:
                src_path = file_info['path']
                dst_path = os.path.join(question_dir, file_info['filename'])
                try:
                    shutil.copy2(src_path, dst_path)
                    stats['copied_files'] += 1
                    has_images = True
                    print(f"复制question图片: {src_path} -> {dst_path}")
                except Exception as e:
                    print(f"复制question图片失败: {src_path} - {e}")
        
        # 复制答案文件
        has_answers = False
        if 'answers' in file_data:
            for file_info in file_data['answers']:
                src_path = file_info['path']
                dst_path = os.path.join(answers_dir, file_info['filename'])
                
                try:
                    shutil.copy2(src_path, dst_path)
                    stats['copied_files'] += 1
                    has_answers = True
                    print(f"复制答案: {src_path} -> {dst_path}")
                except Exception as e:
                    print(f"复制答案失败: {src_path} - {e}")
        
        # 记录缺失的文件
        if not has_images:
            stats['missing_images'].append(file_id)
        if not has_answers:
            stats['missing_answers'].append(file_id)
        
        # 创建README文件
        readme_content = f"""# 测试数据 ID: {file_id}

## 文件清单

### 图片文件
"""

        # 分别列出各个文件夹的内容
        if 'scanned_images' in file_data:
            readme_content += f"\n#### scanned/ 文件夹\n"
            for file_info in file_data['scanned_images']:
                readme_content += f"- {file_info['filename']} (类型: {file_info['type']})\n"

        if 'mobile_images' in file_data:
            readme_content += f"\n#### mobile/ 文件夹\n"
            for file_info in file_data['mobile_images']:
                readme_content += f"- {file_info['filename']} (类型: {file_info['type']})\n"

        if 'question_images' in file_data:
            readme_content += f"\n#### question/ 文件夹\n"
            for file_info in file_data['question_images']:
                readme_content += f"- {file_info['filename']} (类型: {file_info['type']})\n"
        
        readme_content += "\n### 答案文件 (answers/)\n"
        if 'answers' in file_data:
            for file_info in file_data['answers']:
                readme_content += f"- {file_info['filename']} (类型: {file_info['type']})\n"
        else:
            readme_content += "- 无答案文件\n"
        
        # 写入README文件
        readme_path = os.path.join(id_dir, 'README.md')
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
    
    return stats

def generate_summary_report(stats, output_dir):
    """生成汇总报告"""
    report_content = f"""# 测试数据整理报告

## 整理统计
- 总共处理的ID数量: {stats['total_ids']}
- 复制的文件总数: {stats['copied_files']}
- 缺少图片的ID数量: {len(stats['missing_images'])}
- 缺少答案的ID数量: {len(stats['missing_answers'])}

## 缺少图片的ID列表
"""
    
    if stats['missing_images']:
        for file_id in stats['missing_images']:
            report_content += f"- {file_id}\n"
    else:
        report_content += "无\n"
    
    report_content += "\n## 缺少答案的ID列表\n"
    
    if stats['missing_answers']:
        for file_id in stats['missing_answers']:
            report_content += f"- {file_id}\n"
    else:
        report_content += "无\n"
    
    report_content += f"""
## 目录结构说明
```
{output_dir}/
├── [ID1]/
│   ├── scanned/         # 来自scanned目录的图片
│   ├── mobile/          # 来自mobile目录的图片
│   ├── question/        # 来自question目录的图片
│   ├── answers/         # 所有相关答案
│   └── README.md        # 该ID的详细信息
├── [ID2]/
│   └── ...
└── SUMMARY_REPORT.md    # 本报告
```
"""
    
    # 写入报告文件
    report_path = os.path.join(output_dir, 'SUMMARY_REPORT.md')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"\n汇总报告已生成: {report_path}")

def main():
    """主函数"""
    print("开始整理测试数据...")
    
    # 扫描目录
    print("正在扫描目录...")
    data_map = scan_directories()
    
    print(f"发现 {len(data_map)} 个不同的ID")
    
    # 创建整理后的结构
    print("正在创建整理后的目录结构...")
    stats = create_organized_structure(data_map)
    
    # 生成汇总报告
    print("正在生成汇总报告...")
    generate_summary_report(stats, 'organized_test_data')
    
    print(f"""
整理完成！
- 处理了 {stats['total_ids']} 个ID
- 复制了 {stats['copied_files']} 个文件
- 缺少图片的ID: {len(stats['missing_images'])} 个
- 缺少答案的ID: {len(stats['missing_answers'])} 个

请查看 organized_test_data/ 目录和 SUMMARY_REPORT.md 文件获取详细信息。
""")

if __name__ == "__main__":
    main()
