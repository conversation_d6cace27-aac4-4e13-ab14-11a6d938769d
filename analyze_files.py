#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析三个文件夹中的图片文件
"""

import os
import re
from collections import defaultdict

def extract_id_from_filename(filename):
    """从文件名中提取ID"""
    match = re.match(r'(\d{10})', filename)
    if match:
        return match.group(1)
    return None

def analyze_directories():
    """分析三个目录中的文件"""
    directories = ['scanned', 'mobile', 'question']
    
    total_files = 0
    id_count = defaultdict(int)
    file_details = defaultdict(list)
    
    for dir_name in directories:
        if os.path.exists(dir_name):
            print(f"\n=== {dir_name.upper()} 文件夹 ===")
            files = [f for f in os.listdir(dir_name) if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
            print(f"文件数量: {len(files)}")
            
            for filename in files:
                file_id = extract_id_from_filename(filename)
                if file_id:
                    id_count[file_id] += 1
                    file_details[file_id].append(f"{dir_name}/{filename}")
                    total_files += 1
                    print(f"  {filename} -> ID: {file_id}")
                else:
                    print(f"  {filename} -> 无法提取ID")
    
    print(f"\n=== 汇总统计 ===")
    print(f"总文件数: {total_files}")
    print(f"不同ID数: {len(id_count)}")
    
    print(f"\n=== 每个ID的文件数量 ===")
    for file_id in sorted(id_count.keys()):
        count = id_count[file_id]
        print(f"ID {file_id}: {count} 个文件")
        for file_path in file_details[file_id]:
            print(f"  - {file_path}")
    
    # 检查是否有ID有多个文件
    multi_file_ids = {file_id: count for file_id, count in id_count.items() if count > 1}
    if multi_file_ids:
        print(f"\n=== 有多个文件的ID ===")
        for file_id, count in multi_file_ids.items():
            print(f"ID {file_id}: {count} 个文件")

if __name__ == "__main__":
    analyze_directories()
