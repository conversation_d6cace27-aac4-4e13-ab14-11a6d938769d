#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建test_dataset文件夹结构
按照mobile/scanned/question分类，每个分类下有images和answers子文件夹
"""

import os
import shutil
import re

def extract_id_from_filename(filename):
    """从文件名中提取ID"""
    match = re.match(r'(\d{10})', filename)
    if match:
        return match.group(1)
    return None

def get_file_type_from_filename(filename):
    """从文件名中提取文件类型"""
    match = re.search(r'-([qsta]+)(?:-(\d+))?', filename)
    if match:
        file_type = match.group(1)
        number = match.group(2) if match.group(2) else ""
        return f"{file_type}{'-' + number if number else ''}"
    return "unknown"

def find_matching_answers(file_id, file_type, answers_dir):
    """查找匹配的答案文件"""
    matching_answers = []
    if os.path.exists(answers_dir):
        for filename in os.listdir(answers_dir):
            if filename.lower().endswith(('.txt', '.docx')):
                answer_id = extract_id_from_filename(filename)
                answer_type = get_file_type_from_filename(filename)
                if answer_id == file_id and answer_type == file_type:
                    matching_answers.append(filename)
    return matching_answers

def create_test_dataset():
    """创建test_dataset文件夹结构"""
    output_dir = 'test_dataset'
    
    # 删除已存在的文件夹
    if os.path.exists(output_dir):
        print(f"删除已存在的 {output_dir} 文件夹...")
        shutil.rmtree(output_dir)
    
    # 创建主文件夹和子文件夹结构
    categories = ['mobile', 'scanned', 'question']
    for category in categories:
        images_dir = os.path.join(output_dir, category, 'images')
        answers_dir = os.path.join(output_dir, category, 'answers')
        os.makedirs(images_dir, exist_ok=True)
        os.makedirs(answers_dir, exist_ok=True)
        print(f"创建文件夹: {images_dir}")
        print(f"创建文件夹: {answers_dir}")
    
    stats = {
        'total_images': 0,
        'total_answers': 0,
        'missing_answers': []
    }
    
    # 处理每个类别
    for category in categories:
        print(f"\n=== 处理 {category.upper()} 类别 ===")
        
        if not os.path.exists(category):
            print(f"源文件夹 {category} 不存在，跳过...")
            continue
        
        target_images_dir = os.path.join(output_dir, category, 'images')
        target_answers_dir = os.path.join(output_dir, category, 'answers')
        
        # 复制图片文件
        for filename in os.listdir(category):
            if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                file_id = extract_id_from_filename(filename)
                file_type = get_file_type_from_filename(filename)
                
                if file_id:
                    # 复制图片
                    src_path = os.path.join(category, filename)
                    dst_path = os.path.join(target_images_dir, filename)
                    shutil.copy2(src_path, dst_path)
                    stats['total_images'] += 1
                    print(f"复制图片: {filename} -> {category}/images/")
                    
                    # 查找并复制对应的答案文件
                    matching_answers = find_matching_answers(file_id, file_type, 'answers')
                    
                    if matching_answers:
                        for answer_file in matching_answers:
                            src_answer_path = os.path.join('answers', answer_file)
                            dst_answer_path = os.path.join(target_answers_dir, answer_file)
                            shutil.copy2(src_answer_path, dst_answer_path)
                            stats['total_answers'] += 1
                            print(f"  -> 复制答案: {answer_file}")
                    else:
                        stats['missing_answers'].append(f"{category}/{filename}")
                        print(f"  -> 警告: 未找到对应答案 (ID: {file_id}, 类型: {file_type})")
    
    # 生成汇总报告
    generate_summary_report(stats, output_dir)
    
    print(f"\n=== 整理完成 ===")
    print(f"总图片数: {stats['total_images']}")
    print(f"总答案数: {stats['total_answers']}")
    print(f"缺少答案的图片: {len(stats['missing_answers'])}")

def generate_summary_report(stats, output_dir):
    """生成汇总报告"""
    report_content = f"""# Test Dataset 整理报告

## 整理统计
- 总图片数: {stats['total_images']}
- 总答案数: {stats['total_answers']}
- 缺少答案的图片数: {len(stats['missing_answers'])}

## 目录结构
```
test_dataset/
├── mobile/
│   ├── images/          # mobile文件夹中的图片
│   └── answers/         # 对应的答案文件
├── scanned/
│   ├── images/          # scanned文件夹中的图片
│   └── answers/         # 对应的答案文件
└── question/
    ├── images/          # question文件夹中的图片
    └── answers/         # 对应的答案文件
```

## 缺少答案的图片列表
"""
    
    if stats['missing_answers']:
        for missing_file in stats['missing_answers']:
            report_content += f"- {missing_file}\n"
    else:
        report_content += "无\n"
    
    # 写入报告文件
    report_path = os.path.join(output_dir, 'SUMMARY_REPORT.md')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"\n汇总报告已生成: {report_path}")

if __name__ == "__main__":
    create_test_dataset()
